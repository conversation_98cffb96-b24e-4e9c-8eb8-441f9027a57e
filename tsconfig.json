{
  "compilerOptions": {
    "jsx": "react-jsx", // 或 "react"
    "target": "ES6",
    "allowJs": true,
    "module": "commonjs",
    "skipLibCheck": true,
    "esModuleInterop": true,
    "noImplicitAny": true,
    "sourceMap": true,
    "baseUrl": ".",
    "outDir": "dist",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "paths": {
      "*": ["node_modules/*"]
    },
    "allowSyntheticDefaultImports": true
  },
  "include": ["src/**/*"]
}
