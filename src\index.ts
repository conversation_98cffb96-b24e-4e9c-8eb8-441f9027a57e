import {
  BrowserWindow,
  app,
  ipcMain,
  desktopCapturer,
  session,
} from "electron";
// This allows TypeScript to pick up the magic constants that's auto-generated by Forge's Webpack
// plugin that tells the Electron app where to look for the Webpack-bundled app code (depending on
// whether you're running in development or production).
declare const MAIN_WINDOW_WEBPACK_ENTRY: string;
declare const MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY: string;
import Prism from "prismjs";
import OpenAI from "openai";
import axios from "axios";
import FormData from "form-data";
import fs from "fs";
import path from "path";
import { Buffer } from "buffer";
import { createClient, LiveTranscriptionEvents } from "@deepgram/sdk";

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
import electronSquirrelStartup from "electron-squirrel-startup";

if (electronSquirrelStartup) {
  app.quit();
}

const createWindow = (): void => {
  const mainWindow = new BrowserWindow({
    height: 1000,
    width: 1300,
    webPreferences: {
      preload: MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY,
      contextIsolation: true,
      nodeIntegration: false,
      webSecurity: false,
    },
  });

  mainWindow.webContents.session.webRequest.onHeadersReceived(
    (details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          "Content-Security-Policy": [
            "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob:; connect-src 'self' https://api.openai.com;",
          ],
        },
      });
    }
  );

  mainWindow.webContents.session.setPermissionRequestHandler(
    (webContents, permission, callback) => {
      if (permission === "media") {
        callback(true);
      } else {
        callback(false);
      }
    }
  );

  mainWindow.loadURL(MAIN_WINDOW_WEBPACK_ENTRY + "#/main_window");
//  mainWindow.webContents.openDevTools();

  mainWindow.webContents.on("did-finish-load", () => {
    mainWindow.webContents.executeJavaScript(`
      console.log('Applied CSP:', document.querySelector('meta[http-equiv="Content-Security-Policy"]')?.getAttribute('content'));
    `);
  });
};

ipcMain.handle(
  "save-temp-audio-file",
  async (event, audioBuffer: ArrayBuffer) => {
    try {
      const tempFilePath = path.join(
        app.getPath("temp"),
        `temp_audio_${Date.now()}.wav`
      );
      fs.writeFileSync(tempFilePath, Buffer.from(audioBuffer));
      return tempFilePath;
    } catch (error) {
      throw error;
    }
  }
);

ipcMain.handle(
  "transcribe-audio-file",
  async (event, filePath: string, config) => {
    try {
      const formData = new FormData();
      formData.append("file", fs.createReadStream(filePath), "audio.wav");
      formData.append("model", "whisper-1");

      if (config.primaryLanguage && config.primaryLanguage !== "auto") {
        formData.append("language", config.primaryLanguage);
      }
      if (config.secondaryLanguage) {
        formData.append(
          "prompt",
          `This audio may contain ${config.primaryLanguage} and ${config.secondaryLanguage}.`
        );
      }

      const baseUrl = normalizeApiBaseUrl(config.api_base);
      const apiUrl = `${baseUrl}/audio/transcriptions`;
      const response = await axios.post(apiUrl, formData, {
        headers: {
          ...formData.getHeaders(),
          Authorization: `Bearer ${config.openai_key}`,
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      });

      return response.data;
    } catch (error) {
      throw error;
    } finally {
      fs.unlinkSync(filePath);
    }
  }
);

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on("ready", createWindow);

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the
// dock icon is clicked and there are no other windows open.
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.

import ElectronStore from "electron-store";

interface StoreSchema {
  config: Record<string, any>;
}

type TypedElectronStore = ElectronStore<StoreSchema> & {
  get: <K extends keyof StoreSchema>(key: K) => StoreSchema[K];
  set: <K extends keyof StoreSchema>(key: K, value: StoreSchema[K]) => void;
  clear: () => void;
};

const store = new ElectronStore<StoreSchema>() as TypedElectronStore;

ipcMain.handle("get-config", () => {
  return store.get("config");
});

ipcMain.handle("set-config", (event, config) => {
  store.set("config", config);
});

ipcMain.handle("parsePDF", async (event, pdfBuffer) => {
  try {
    const pdf = require("pdf-parse");
    const data = await pdf(Buffer.from(pdfBuffer), {
      max: 0,
    });
    return { text: data.text };
  } catch (error) {
    return { error: "Failed to parse PDF: " + error.message };
  }
});

ipcMain.handle("process-image", async (event, imageData) => {
  try {
    const sharp = require("sharp");
    let image;
    if (imageData.startsWith("data:image")) {
      const base64Data = imageData.split(",")[1];
      const imageBuffer = Buffer.from(base64Data, "base64");
      image = sharp(imageBuffer);
    } else {
      throw new Error(
        "Invalid image input: expected Base64 encoded image data"
      );
    }
    const metadata = await image.metadata();
    return `Image size: ${metadata.width}x${metadata.height}, Format: ${metadata.format}`;
  } catch (error) {
    return { error: "Failed to process image: " + error.message };
  }
});

ipcMain.handle("highlightCode", async (event, code, language) => {
  return Prism.highlight(code, Prism.languages[language], language);
});

app.on("before-quit", () => {
  const config = store.get("config") || {};
  const apiInfo = {
    openai_key: config.openai_key || "",
    api_base: config.api_base || "",
    gpt_model: config.gpt_model || "",
    api_call_method: config.api_call_method || "direct",
    primaryLanguage: config.primaryLanguage || "en",
    deepgram_api_key: config.deepgram_api_key || "",
  };
  store.clear();
  store.set("config", apiInfo);
});

ipcMain.handle("get-system-audio-stream", async () => {
  try {
    const sources = await desktopCapturer.getSources({
      types: ["window", "screen"],
      fetchWindowIcons: false,
    });
    const audioSources = sources.filter(
      (source) =>
        source.name.toLowerCase().includes("sound") ||
        source.name.toLowerCase().includes("audio")
    );
    return audioSources.map((source) => source.id);
  } catch (error) {
    throw error;
  }
});

app.on("ready", () => {
  session.defaultSession.setDisplayMediaRequestHandler(
    (request, callback) => {
      desktopCapturer.getSources({ types: ["screen"] }).then((sources) => {
        callback({ video: sources[0], audio: "loopback" });
      });
    },
    { useSystemPicker: true }
  );

  ipcMain.handle(
    "transcribe-audio",
    async (event, audioBuffer: ArrayBuffer, config) => {
      try {
        const tempFilePath = path.join(
          app.getPath("temp"),
          `temp_audio_${Date.now()}.wav`
        );
        fs.writeFileSync(tempFilePath, Buffer.from(audioBuffer));

        const formData = new FormData();
        formData.append("file", fs.createReadStream(tempFilePath), {
          filename: "audio.wav",
          contentType: "audio/wav",
        });
        formData.append("model", "whisper-1");

        if (config.primaryLanguage && config.primaryLanguage !== "auto") {
          formData.append("language", config.primaryLanguage);
        }
        if (config.secondaryLanguage) {
          formData.append(
            "prompt",
            `This audio may contain ${config.primaryLanguage} and ${config.secondaryLanguage}.`
          );
        }

        const baseUrl = normalizeApiBaseUrl(config.api_base);
        const apiUrl = `${baseUrl}/audio/transcriptions`;
        const response = await axios.post(apiUrl, formData, {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${config.openai_key}`,
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        });

        fs.unlinkSync(tempFilePath);

        return response.data;
      } catch (error) {
        throw error;
      }
    }
  );
});

ipcMain.handle("test-api-config", async (event, config) => {
  try {
    const axiosInstance = axios.create({
      baseURL: normalizeApiBaseUrl(config.api_base),
      headers: {
        Authorization: `Bearer ${config.openai_key}`,
        "Content-Type": "application/json",
      },
    });

    const response = await axiosInstance.post("/chat/completions", {
      model: config.gpt_model || "gpt-3.5-turbo",
      messages: [{ role: "user", content: "Hello, this is a test." }],
    });

    if (
      response.data.choices &&
      response.data.choices[0] &&
      response.data.choices[0].message
    ) {
      return { success: true };
    } else {
      return { success: false, error: "Unexpected API response structure" };
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response) {
        return {
          success: false,
          error: `Server responded with error: ${error.response.status} ${error.response.statusText}`,
        };
      } else if (error.request) {
        return {
          success: false,
          error:
            "No response received from server. Please check your network connection and API base URL.",
        };
      } else {
        return {
          success: false,
          error: `Error setting up the request: ${error.message}`,
        };
      }
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
});

ipcMain.handle("callOpenAI", async (event, { config, messages, signal }) => {
  try {
    const openai = new OpenAI({
      apiKey: config.openai_key,
      baseURL: normalizeApiBaseUrl(config.api_base),
    });

    const abortController = new AbortController();
    if (signal) {
      signal.addEventListener('abort', () => abortController.abort());
    }

    const response = await openai.chat.completions.create({
      model: config.gpt_model || "gpt-3.5-turbo",
      messages: messages,
    }, { signal: abortController.signal });

    if (
      !response.choices ||
      !response.choices[0] ||
      !response.choices[0].message
    ) {
      throw new Error("Unexpected API response structure");
    }
    return { content: response.choices[0].message.content };
  } catch (error) {
    if (error.name === "AbortError") {
      return { error: "AbortError" };
    }
    return { error: error.message || "Unknown error occurred" };
  }
});

ipcMain.handle("get-desktop-sources", async () => {
  try {
    const sources = await desktopCapturer.getSources({
      types: ["window", "screen"],
    });
    return sources.map((source) => ({
      id: source.id,
      name: source.name,
      thumbnail: source.thumbnail.toDataURL(),
    }));
  } catch (error) {
    return [];
  }
});

function normalizeApiBaseUrl(url: string): string {
  if (!url) return "https://api.openai.com/v1";
  url = url.trim();
  if (!url.startsWith("http://") && !url.startsWith("https://")) {
    url = "https://" + url;
  }
  if (!url.endsWith("/v1")) {
    url = url.endsWith("/") ? url + "v1" : url + "/v1";
  }
  return url;
}

let deepgramConnection: any = null;

ipcMain.handle("start-deepgram", async (event, config) => {
  try {
    if (!config.deepgram_key) {
      throw new Error("Deepgram API key lose");
    }
    const deepgram = createClient(config.deepgram_key);
    deepgramConnection = deepgram.listen.live({
      punctuate: true,
      interim_results: false,
      model: "nova-2",
      // model: "general",
      language: config.primaryLanguage || "en",
      encoding: "linear16",
      sample_rate: 16000,
      endpointing: 1500,
    });

    deepgramConnection.addListener(LiveTranscriptionEvents.Open, () => {
      event.sender.send("deepgram-status", { status: "open" });
    });

    deepgramConnection.addListener(LiveTranscriptionEvents.Close, () => {
      event.sender.send("deepgram-status", { status: "closed" });
    });

    deepgramConnection.addListener(
      LiveTranscriptionEvents.Transcript,
      (data: any) => {
        if (
          data &&
          data.is_final &&
          data.channel &&
          data.channel.alternatives &&
          data.channel.alternatives[0]
        ) {
          const transcript = data.channel.alternatives[0].transcript;
          if (transcript) {
            event.sender.send("deepgram-transcript", {
              transcript,
              is_final: true,
            });
          }
        }
      }
    );

    deepgramConnection.addListener(
      LiveTranscriptionEvents.Error,
      (err: any) => {
        event.sender.send("deepgram-error", err);
      }
    );

    await new Promise((resolve, reject) => {
      deepgramConnection.addListener(LiveTranscriptionEvents.Open, resolve);
      deepgramConnection.addListener(LiveTranscriptionEvents.Error, reject);
      setTimeout(() => reject(new Error("Deepgram timeout")), 10000);
    });

    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle("send-audio-to-deepgram", async (event, audioData) => {
  if (deepgramConnection) {
    try {
      const buffer = Buffer.from(audioData);
      deepgramConnection.send(buffer);
    } catch (error) {
      console.error("failed send data to Deepgram :", error);
    }
  }
});

ipcMain.handle("stop-deepgram", () => {
  if (deepgramConnection) {
    deepgramConnection.finish();
    deepgramConnection = null;
  }
});
