{"name": "interview-assistant", "productName": "Interview-Assistant", "version": "1.0.0", "description": "My Electron application description", "main": ".webpack/main", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish"}, "dependencies": {"@deepgram/sdk": "^3.6.0", "@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1", "@reduxjs/toolkit": "^2.2.7", "@types/node-fetch": "^2.6.11", "@types/react-router-dom": "^5.3.3", "axios": "^1.7.7", "cross-fetch": "^4.0.0", "daisyui": "^4.12.10", "electron-squirrel-startup": "^1.0.1", "electron-store": "^10.0.0", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.0", "highlight.js": "^11.7.0", "node-fetch": "^2.7.0", "openai": "^4.60.0", "pdf-parse": "^1.1.1", "prismjs": "^1.29.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.26.2", "react-syntax-highlighter": "^15.5.0", "sharp": "^0.33.5", "tailwindcss": "^3.4.11"}, "devDependencies": {"@electron-forge/cli": "^7.4.0", "@electron-forge/maker-deb": "^7.4.0", "@electron-forge/maker-dmg": "^7.4.0", "@electron-forge/maker-rpm": "^7.4.0", "@electron-forge/maker-squirrel": "^7.4.0", "@electron-forge/maker-zip": "^7.4.0", "@electron-forge/plugin-auto-unpack-natives": "^7.4.0", "@electron-forge/plugin-fuses": "^7.4.0", "@electron-forge/plugin-webpack": "^7.4.0", "@electron/fuses": "^1.8.0", "@types/electron-squirrel-startup": "^1.0.2", "@types/fluent-ffmpeg": "^2.1.26", "@types/node": "^22.5.4", "@types/pdf-parse": "^1.1.4", "@types/prismjs": "^1.26.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vercel/webpack-asset-relocator-loader": "^1.7.3", "autoprefixer": "^10.4.20", "css-loader": "^6.11.0", "electron": "^32.1.0", "electron-builder": "^25.0.5", "eslint": "^8.57.0", "eslint-plugin-import": "^2.30.0", "fork-ts-checker-webpack-plugin": "^7.3.0", "node-loader": "^2.0.0", "postcss": "^8.4.45", "postcss-loader": "^8.1.1", "style-loader": "^3.3.4", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "keywords": [], "author": {"name": "nohairblingbling", "email": "<EMAIL>"}, "license": "MIT", "build": {"appId": "com.example.interviewassistant", "productName": "Interview Assistant", "mac": {"category": "public.app-category.productivity", "target": ["dmg"], "icon": "./src/assets/icon.icns"}, "dmg": {"icon": "./src/assets/icon.icns", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "win": {"target": ["zip"], "icon": "./src/assets/icon.ico"}}, "permissions": ["audioCapture", "desktopCapture"]}